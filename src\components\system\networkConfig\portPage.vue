<script lang="ts" setup>
import { useI18n } from 'vue-i18n'
import PortTable from '@/components/system/list/PortTable.vue'

const { t } = useI18n()

const IPInfo: any = ref({
  map_port_disable: '0',
  map_lan_ip: '',
  wan_pc_ip: '',
  port_num: '',
})

const submitIP = async () => {
  portForm.value.validate().then(async (result: any) => {
    if (result.valid) {
      const data = await $api('', {
        requestType: 268,
        data: {
          map_port_disable: IPInfo.value.map_port_disable,
          map_lan_port_ip: IPInfo.value.map_lan_ip,
          wan_pc_ip: IPInfo.value.wan_pc_ip,
          port_num: IPInfo.value.port_num,
        },
      })

      if (data.err_code == 0) {
        IPInfo.value = {
          map_port_disable: '0',
          map_lan_ip: '',
          wan_pc_ip: '',
          port_num: '',
        }

        // 重置表单
        portForm.value.reset()
        setTimeout(() => {
          portRef.value.getPortList()
        }, 500)
      }
      else {
        setTimeout(() => {
          portRef.value.getPortList()
        }, 500)
      }
    }
  })
}

const portRef: any = ref(null)
const portForm: any = ref(null)

const requiredValidator = (value: any) => {
  return !!value || t('NetworkConfig.Port.Required')
}

const ipv4Validator = (value: any) => {
  const ipRegex = /^(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})$/

  return ipRegex.test(value) || t('NetworkConfig.Port.InvalidIP')
}

const ipv4ValidatorNew = (value: any) => {
  if (!value)
    return true

  const ipRegex = /^(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})$/

  return ipRegex.test(value) || t('NetworkConfig.Port.InvalidIP')
}

const portList: any = ref([])

const portValidator = (value: any) => {
  // 端口格式正则
  const singlePort = /^([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/
  const rangePort = /^([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])-([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/

  let inputStart: number, inputEnd: number

  if (singlePort.test(value)) {
    inputStart = inputEnd = Number(value)
  }
  else if (rangePort.test(value)) {
    const match = value.match(rangePort)

    inputStart = Number(match[1])
    inputEnd = Number(match[2])
    if (inputStart >= inputEnd)
      return t('NetworkConfig.Port.InvalidPortRange')
  }
  else {
    return t('NetworkConfig.Port.InvalidPort')
  }

  // 先检查端口是否被占用
  for (const item of portList.value) {
    const portStr = item.port_num
    if (singlePort.test(portStr)) {
      const existPort = Number(portStr)
      if (
        (inputStart <= existPort && inputEnd >= existPort)
      )
        return t('NetworkConfig.Port.Occupied') // 你需要在 i18n 里加这个字段
    }
    else if (rangePort.test(portStr)) {
      const match = portStr.match(rangePort)
      const existStart = Number(match[1])
      const existEnd = Number(match[2])

      // 判断区间是否有重叠
      if (!(inputEnd < existStart || inputStart > existEnd))
        return t('NetworkConfig.Port.Occupied')
    }
  }

  // 范围校验
  if (inputStart < 1 || inputEnd > 65535)
    return t('NetworkConfig.Port.InvalidPort')

  return true
}

const handleUpdatet = (data: any) => {
  portList.value = data
}
</script>

<template>
  <VCard
    class="mb-5"
    :title="t('NetworkConfig.Port.Title')"
  >
    <VCardText>
      <div class="d-flex align-start mt-2 bg-primary-transparent pa-3 rounded border border-primary">
        <VIcon
          icon="tabler-alert-circle"
          class="mr-6"
        />
        <ol class="text-primary text-subtitle-1">
          <div>{{ t('NetworkConfig.Port.Description') }}</div>
          <li>{{ t('NetworkConfig.Port.Points.Point1') }}</li>
          <li>{{ t('NetworkConfig.Port.Points.Point2') }}</li>
          <li>{{ t('NetworkConfig.Port.Points.Point3') }}</li>
          <li>{{ t('NetworkConfig.Port.Points.Point4') }}</li>
          <li>{{ t('NetworkConfig.Port.Points.Point5') }}</li>
        </ol>
      </div>

      <VCard
        class="mt-6"
        :title="t('NetworkConfig.Port.AddPortMapping')"
      >
        <VCardText>
          <VForm ref="portForm">
            <VRow class="match-height">
              <VCol
                cols="12"
                md="4"
              >
                <AppTextField
                  v-model="IPInfo.map_lan_ip"
                  :label="t('NetworkConfig.Port.DeviceIP')"
                  :placeholder="t('NetworkConfig.Port.EnterDeviceIP')"
                  :rules="[requiredValidator, ipv4Validator]"
                />
              </VCol>
              <VCol
                cols="12"
                md="4"
              >
                <AppTextField
                  v-model="IPInfo.wan_pc_ip"
                  :label="t('NetworkConfig.Port.WANPCIP')"
                  :placeholder="t('NetworkConfig.Port.EnterWANPCIP')"
                  :rules="[ipv4ValidatorNew]"
                />
              </VCol>
              <VCol
                cols="12"
                md="4"
              >
                <AppTextField
                  v-model="IPInfo.port_num"
                  :label="t('NetworkConfig.Port.Port')"
                  :placeholder="t('NetworkConfig.Port.EnterPort')"
                  :rules="[requiredValidator, portValidator]"
                />
              </VCol>
            </VRow>
          </VForm>
          <div
            class="mt-2"
            style="display: flex;justify-content: flex-end;"
          >
            <VBtn
              color="primary"
              @click="submitIP"
            >
              {{ t('NetworkConfig.Port.AddRule') }}
            </VBtn>
          </div>
        </VCardText>
      </VCard>
    </VCardText>
  </VCard>
  <VCard class="mb-5">
    <VCardText>
      <PortTable
        ref="portRef"
        @update="handleUpdatet"
      />
    </VCardText>
  </VCard>
</template>

<style lang="scss" scoped>
.blueArea {
  p {
    font-family: "PingFang SC";
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;

    /* 160% */
    margin-block-end: 0;
  }

  .bold {
    font-family: "PingFang SC";
    font-size: 15px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
  }

  .text {
    position: relative;
  }

  .text::before {
    position: absolute;
    border-radius: 3px;
    block-size: 6px;
    content: "";
    inline-size: 6px;
    inset-block-start: 9px;
    inset-inline-start: 9px;
  }
}
</style>
